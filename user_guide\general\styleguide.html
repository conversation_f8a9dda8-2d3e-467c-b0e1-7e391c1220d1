

<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en" > <!--<![endif]-->
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>PHP Style Guide &mdash; CodeIgniter 3.1.11 documentation</title>
  

  
  
    <link rel="shortcut icon" href="../_static/ci-icon.ico"/>
  

  
  <link href='https://fonts.googleapis.com/css?family=Lato:400,700,400italic,700italic|Roboto+Slab:400,700|Inconsolata:400,700&subset=latin,cyrillic' rel='stylesheet' type='text/css'>

  
  
    

  

  
  
    <link rel="stylesheet" href="../_static/css/citheme.css" type="text/css" />
  

  
        <link rel="index" title="Index"
              href="../genindex.html"/>
        <link rel="search" title="Search" href="../search.html"/>
    <link rel="top" title="CodeIgniter 3.1.11 documentation" href="../index.html"/>
        <link rel="up" title="General Topics" href="index.html"/>
        <link rel="next" title="Libraries" href="../libraries/index.html"/>
        <link rel="prev" title="Security" href="security.html"/> 

  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.6.2/modernizr.min.js"></script>

</head>

<body class="wy-body-for-nav" role="document">

  <div id="nav">
  <div id="nav_inner">
    
    
    
      <div id="pulldown-menu" class="ciNav">
        <ul>
<li class="toctree-l1"><a class="reference internal" href="welcome.html">Welcome to CodeIgniter</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation/index.html">Installation Instructions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../installation/downloads.html">Downloading CodeIgniter</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/index.html">Installation Instructions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/upgrading.html">Upgrading From a Previous Version</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/troubleshooting.html">Troubleshooting</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../overview/index.html">CodeIgniter Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../overview/getting_started.html">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/at_a_glance.html">CodeIgniter at a Glance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/features.html">Supported Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/appflow.html">Application Flow Chart</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/mvc.html">Model-View-Controller</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/goals.html">Architectural Goals</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../tutorial/index.html">Tutorial</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/static_pages.html">Static pages</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/news_section.html">News section</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/create_news_items.html">Create news items</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/conclusion.html">Conclusion</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../contributing/index.html">Contributing to CodeIgniter</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation/index.html">Writing CodeIgniter Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../DCO.html">Developer’s Certificate of Origin 1.1</a></li>
</ul>
</li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">General Topics</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="urls.html">CodeIgniter URLs</a></li>
<li class="toctree-l2"><a class="reference internal" href="controllers.html">Controllers</a></li>
<li class="toctree-l2"><a class="reference internal" href="reserved_names.html">Reserved Names</a></li>
<li class="toctree-l2"><a class="reference internal" href="views.html">Views</a></li>
<li class="toctree-l2"><a class="reference internal" href="models.html">Models</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers.html">Helpers</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries.html">Using CodeIgniter Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="creating_libraries.html">Creating Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="drivers.html">Using CodeIgniter Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="creating_drivers.html">Creating Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="core_classes.html">Creating Core System Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="ancillary_classes.html">Creating Ancillary Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="hooks.html">Hooks - Extending the Framework Core</a></li>
<li class="toctree-l2"><a class="reference internal" href="autoloader.html">Auto-loading Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="common_functions.html">Common Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatibility_functions.html">Compatibility Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="routing.html">URI Routing</a></li>
<li class="toctree-l2"><a class="reference internal" href="errors.html">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="caching.html">Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiling.html">Profiling Your Application</a></li>
<li class="toctree-l2"><a class="reference internal" href="cli.html">Running via the CLI</a></li>
<li class="toctree-l2"><a class="reference internal" href="managing_apps.html">Managing your Applications</a></li>
<li class="toctree-l2"><a class="reference internal" href="environments.html">Handling Multiple Environments</a></li>
<li class="toctree-l2"><a class="reference internal" href="alternative_php.html">Alternate PHP Syntax for View Files</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">PHP Style Guide</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../libraries/index.html">Libraries</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../libraries/benchmark.html">Benchmarking Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/caching.html">Caching Driver</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/calendar.html">Calendaring Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/cart.html">Shopping Cart Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/config.html">Config Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/email.html">Email Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/encrypt.html">Encrypt Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/encryption.html">Encryption Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/file_uploading.html">File Uploading Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/form_validation.html">Form Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/ftp.html">FTP Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/image_lib.html">Image Manipulation Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/input.html">Input Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/javascript.html">Javascript Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/language.html">Language Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/loader.html">Loader Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/migration.html">Migrations Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/output.html">Output Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/pagination.html">Pagination Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/parser.html">Template Parser Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/security.html">Security Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/sessions.html">Session Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/table.html">HTML Table Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/trackback.html">Trackback Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/typography.html">Typography Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/unit_testing.html">Unit Testing Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/uri.html">URI Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/user_agent.html">User Agent Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/xmlrpc.html">XML-RPC and XML-RPC Server Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/zip.html">Zip Encoding Class</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../database/index.html">Database Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../database/examples.html">Quick Start: Usage Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/configuration.html">Database Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/connecting.html">Connecting to a Database</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/queries.html">Running Queries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/results.html">Generating Query Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/helpers.html">Query Helper Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/query_builder.html">Query Builder Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/transactions.html">Transactions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/metadata.html">Getting MetaData</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/call_function.html">Custom Function Calls</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/caching.html">Query Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/forge.html">Database Manipulation with Database Forge</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/utilities.html">Database Utilities Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/db_driver_reference.html">Database Driver Reference</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../helpers/index.html">Helpers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../helpers/array_helper.html">Array Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/captcha_helper.html">CAPTCHA Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/cookie_helper.html">Cookie Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/date_helper.html">Date Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/directory_helper.html">Directory Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/download_helper.html">Download Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/email_helper.html">Email Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/file_helper.html">File Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/form_helper.html">Form Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/html_helper.html">HTML Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/inflector_helper.html">Inflector Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/language_helper.html">Language Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/number_helper.html">Number Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/path_helper.html">Path Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/security_helper.html">Security Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/smiley_helper.html">Smiley Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/string_helper.html">String Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/text_helper.html">Text Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/typography_helper.html">Typography Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/url_helper.html">URL Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/xml_helper.html">XML Helper</a></li>
</ul>
</li>
</ul>

      </div>
    
      
  </div>
</div>
<div id="nav2">
  <a href="#" id="openToc">
    <img src="data:image/jpeg;base64,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" title="Toggle Table of Contents" alt="Toggle Table of Contents" />
  </a>
</div>

  <div class="wy-grid-for-nav">

    
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-nav-search">
        
          <a href="../index.html" class="fa fa-home"> CodeIgniter</a>
        
        
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
      </div>

      <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        
          
          
              <ul>
<li class="toctree-l1"><a class="reference internal" href="welcome.html">Welcome to CodeIgniter</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation/index.html">Installation Instructions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../installation/downloads.html">Downloading CodeIgniter</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/index.html">Installation Instructions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/upgrading.html">Upgrading From a Previous Version</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/troubleshooting.html">Troubleshooting</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../overview/index.html">CodeIgniter Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../overview/getting_started.html">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/at_a_glance.html">CodeIgniter at a Glance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/features.html">Supported Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/appflow.html">Application Flow Chart</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/mvc.html">Model-View-Controller</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/goals.html">Architectural Goals</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../tutorial/index.html">Tutorial</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/static_pages.html">Static pages</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/news_section.html">News section</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/create_news_items.html">Create news items</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/conclusion.html">Conclusion</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../contributing/index.html">Contributing to CodeIgniter</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation/index.html">Writing CodeIgniter Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../DCO.html">Developer’s Certificate of Origin 1.1</a></li>
</ul>
</li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">General Topics</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="urls.html">CodeIgniter URLs</a></li>
<li class="toctree-l2"><a class="reference internal" href="controllers.html">Controllers</a></li>
<li class="toctree-l2"><a class="reference internal" href="reserved_names.html">Reserved Names</a></li>
<li class="toctree-l2"><a class="reference internal" href="views.html">Views</a></li>
<li class="toctree-l2"><a class="reference internal" href="models.html">Models</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers.html">Helpers</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries.html">Using CodeIgniter Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="creating_libraries.html">Creating Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="drivers.html">Using CodeIgniter Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="creating_drivers.html">Creating Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="core_classes.html">Creating Core System Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="ancillary_classes.html">Creating Ancillary Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="hooks.html">Hooks - Extending the Framework Core</a></li>
<li class="toctree-l2"><a class="reference internal" href="autoloader.html">Auto-loading Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="common_functions.html">Common Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="compatibility_functions.html">Compatibility Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="routing.html">URI Routing</a></li>
<li class="toctree-l2"><a class="reference internal" href="errors.html">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="caching.html">Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="profiling.html">Profiling Your Application</a></li>
<li class="toctree-l2"><a class="reference internal" href="cli.html">Running via the CLI</a></li>
<li class="toctree-l2"><a class="reference internal" href="managing_apps.html">Managing your Applications</a></li>
<li class="toctree-l2"><a class="reference internal" href="environments.html">Handling Multiple Environments</a></li>
<li class="toctree-l2"><a class="reference internal" href="alternative_php.html">Alternate PHP Syntax for View Files</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">PHP Style Guide</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../libraries/index.html">Libraries</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../libraries/benchmark.html">Benchmarking Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/caching.html">Caching Driver</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/calendar.html">Calendaring Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/cart.html">Shopping Cart Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/config.html">Config Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/email.html">Email Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/encrypt.html">Encrypt Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/encryption.html">Encryption Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/file_uploading.html">File Uploading Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/form_validation.html">Form Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/ftp.html">FTP Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/image_lib.html">Image Manipulation Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/input.html">Input Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/javascript.html">Javascript Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/language.html">Language Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/loader.html">Loader Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/migration.html">Migrations Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/output.html">Output Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/pagination.html">Pagination Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/parser.html">Template Parser Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/security.html">Security Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/sessions.html">Session Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/table.html">HTML Table Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/trackback.html">Trackback Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/typography.html">Typography Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/unit_testing.html">Unit Testing Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/uri.html">URI Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/user_agent.html">User Agent Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/xmlrpc.html">XML-RPC and XML-RPC Server Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="../libraries/zip.html">Zip Encoding Class</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../database/index.html">Database Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../database/examples.html">Quick Start: Usage Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/configuration.html">Database Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/connecting.html">Connecting to a Database</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/queries.html">Running Queries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/results.html">Generating Query Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/helpers.html">Query Helper Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/query_builder.html">Query Builder Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/transactions.html">Transactions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/metadata.html">Getting MetaData</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/call_function.html">Custom Function Calls</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/caching.html">Query Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/forge.html">Database Manipulation with Database Forge</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/utilities.html">Database Utilities Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/db_driver_reference.html">Database Driver Reference</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../helpers/index.html">Helpers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../helpers/array_helper.html">Array Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/captcha_helper.html">CAPTCHA Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/cookie_helper.html">Cookie Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/date_helper.html">Date Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/directory_helper.html">Directory Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/download_helper.html">Download Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/email_helper.html">Email Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/file_helper.html">File Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/form_helper.html">Form Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/html_helper.html">HTML Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/inflector_helper.html">Inflector Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/language_helper.html">Language Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/number_helper.html">Number Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/path_helper.html">Path Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/security_helper.html">Security Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/smiley_helper.html">Smiley Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/string_helper.html">String Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/text_helper.html">Text Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/typography_helper.html">Typography Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/url_helper.html">URL Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/xml_helper.html">XML Helper</a></li>
</ul>
</li>
</ul>

          
        
      </div>
      &nbsp;
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      
      <nav class="wy-nav-top" role="navigation" aria-label="top navigation">
        <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
        <a href="../index.html">CodeIgniter</a>
      </nav>


      
      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="breadcrumbs navigation">
  <ul class="wy-breadcrumbs">
    <li><a href="../index.html">Docs</a> &raquo;</li>
      
        <li><a href="index.html">General Topics</a> &raquo;</li>
      
    <li>PHP Style Guide</li>
    <li class="wy-breadcrumbs-aside">
      
    </li>
    <div style="float:right;margin-left:5px;" id="closeMe">
      <img title="Classic Layout" alt="classic layout" src="data:image/gif;base64,R0lGODlhFAAUAJEAAAAAADMzM////wAAACH5BAUUAAIALAAAAAAUABQAAAImlI+py+0PU5gRBRDM3DxbWoXis42X13USOLauUIqnlsaH/eY6UwAAOw==" />
    </div>
  </ul>
  <hr/>
</div>
          <div role="main" class="document">
            
  <div class="section" id="php-style-guide">
<h1><a class="toc-backref" href="#id1">PHP Style Guide</a><a class="headerlink" href="#php-style-guide" title="Permalink to this headline">¶</a></h1>
<p>The following page describes the coding styles adhered to when
contributing to the development of CodeIgniter. There is no requirement
to use these styles in your own CodeIgniter application, though they
are recommended.</p>
<div class="contents topic" id="table-of-contents">
<p class="topic-title first">Table of Contents</p>
<ul class="simple">
<li><a class="reference internal" href="#php-style-guide" id="id1">PHP Style Guide</a><ul>
<li><a class="reference internal" href="#file-format" id="id2">File Format</a><ul>
<li><a class="reference internal" href="#textmate" id="id3">TextMate</a></li>
<li><a class="reference internal" href="#bbedit" id="id4">BBEdit</a></li>
</ul>
</li>
<li><a class="reference internal" href="#php-closing-tag" id="id5">PHP Closing Tag</a></li>
<li><a class="reference internal" href="#file-naming" id="id6">File Naming</a></li>
<li><a class="reference internal" href="#class-and-method-naming" id="id7">Class and Method Naming</a></li>
<li><a class="reference internal" href="#variable-names" id="id8">Variable Names</a></li>
<li><a class="reference internal" href="#commenting" id="id9">Commenting</a></li>
<li><a class="reference internal" href="#constants" id="id10">Constants</a></li>
<li><a class="reference internal" href="#true-false-and-null" id="id11">TRUE, FALSE, and NULL</a></li>
<li><a class="reference internal" href="#logical-operators" id="id12">Logical Operators</a></li>
<li><a class="reference internal" href="#comparing-return-values-and-typecasting" id="id13">Comparing Return Values and Typecasting</a></li>
<li><a class="reference internal" href="#debugging-code" id="id14">Debugging Code</a></li>
<li><a class="reference internal" href="#whitespace-in-files" id="id15">Whitespace in Files</a></li>
<li><a class="reference internal" href="#compatibility" id="id16">Compatibility</a></li>
<li><a class="reference internal" href="#one-file-per-class" id="id17">One File per Class</a></li>
<li><a class="reference internal" href="#whitespace" id="id18">Whitespace</a></li>
<li><a class="reference internal" href="#line-breaks" id="id19">Line Breaks</a></li>
<li><a class="reference internal" href="#code-indenting" id="id20">Code Indenting</a></li>
<li><a class="reference internal" href="#bracket-and-parenthetic-spacing" id="id21">Bracket and Parenthetic Spacing</a></li>
<li><a class="reference internal" href="#localized-text" id="id22">Localized Text</a></li>
<li><a class="reference internal" href="#private-methods-and-variables" id="id23">Private Methods and Variables</a></li>
<li><a class="reference internal" href="#php-errors" id="id24">PHP Errors</a></li>
<li><a class="reference internal" href="#short-open-tags" id="id25">Short Open Tags</a></li>
<li><a class="reference internal" href="#one-statement-per-line" id="id26">One Statement Per Line</a></li>
<li><a class="reference internal" href="#strings" id="id27">Strings</a></li>
<li><a class="reference internal" href="#sql-queries" id="id28">SQL Queries</a></li>
<li><a class="reference internal" href="#default-function-arguments" id="id29">Default Function Arguments</a></li>
</ul>
</li>
</ul>
</div>
<div class="section" id="file-format">
<h2><a class="toc-backref" href="#id2">File Format</a><a class="headerlink" href="#file-format" title="Permalink to this headline">¶</a></h2>
<p>Files should be saved with Unicode (UTF-8) encoding. The BOM should
<em>not</em> be used. Unlike UTF-16 and UTF-32, there’s no byte order to
indicate in a UTF-8 encoded file, and the BOM can have a negative side
effect in PHP of sending output, preventing the application from being
able to set its own headers. Unix line endings should be used (LF).</p>
<p>Here is how to apply these settings in some of the more common text
editors. Instructions for your text editor may vary; check your text
editor’s documentation.</p>
<div class="section" id="textmate">
<h3><a class="toc-backref" href="#id3">TextMate</a><a class="headerlink" href="#textmate" title="Permalink to this headline">¶</a></h3>
<ol class="arabic simple">
<li>Open the Application Preferences</li>
<li>Click Advanced, and then the “Saving” tab</li>
<li>In “File Encoding”, select “UTF-8 (recommended)”</li>
<li>In “Line Endings”, select “LF (recommended)”</li>
<li><em>Optional:</em> Check “Use for existing files as well” if you wish to
modify the line endings of files you open to your new preference.</li>
</ol>
</div>
<div class="section" id="bbedit">
<h3><a class="toc-backref" href="#id4">BBEdit</a><a class="headerlink" href="#bbedit" title="Permalink to this headline">¶</a></h3>
<ol class="arabic simple">
<li>Open the Application Preferences</li>
<li>Select “Text Encodings” on the left.</li>
<li>In “Default text encoding for new documents”, select “Unicode (UTF-8,
no BOM)”</li>
<li><em>Optional:</em> In “If file’s encoding can’t be guessed, use”, select
“Unicode (UTF-8, no BOM)”</li>
<li>Select “Text Files” on the left.</li>
<li>In “Default line breaks”, select “Mac OS X and Unix (LF)”</li>
</ol>
</div>
</div>
<div class="section" id="php-closing-tag">
<h2><a class="toc-backref" href="#id5">PHP Closing Tag</a><a class="headerlink" href="#php-closing-tag" title="Permalink to this headline">¶</a></h2>
<p>The PHP closing tag on a PHP document <strong>?&gt;</strong> is optional to the PHP
parser. However, if used, any whitespace following the closing tag,
whether introduced by the developer, user, or an FTP application, can
cause unwanted output, PHP errors, or if the latter are suppressed,
blank pages. For this reason, all PHP files MUST OMIT the PHP closing
tag and end with a single empty line instead.</p>
</div>
<div class="section" id="file-naming">
<h2><a class="toc-backref" href="#id6">File Naming</a><a class="headerlink" href="#file-naming" title="Permalink to this headline">¶</a></h2>
<p>Class files must be named in a Ucfirst-like manner, while any other file name
(configurations, views, generic scripts, etc.) should be in all lowercase.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nx">somelibrary</span><span class="o">.</span><span class="nx">php</span>
<span class="nx">someLibrary</span><span class="o">.</span><span class="nx">php</span>
<span class="nx">SOMELIBRARY</span><span class="o">.</span><span class="nx">php</span>
<span class="nx">Some_Library</span><span class="o">.</span><span class="nx">php</span>

<span class="nx">Application_config</span><span class="o">.</span><span class="nx">php</span>
<span class="nx">Application_Config</span><span class="o">.</span><span class="nx">php</span>
<span class="nx">applicationConfig</span><span class="o">.</span><span class="nx">php</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nx">Somelibrary</span><span class="o">.</span><span class="nx">php</span>
<span class="nx">Some_library</span><span class="o">.</span><span class="nx">php</span>

<span class="nx">applicationconfig</span><span class="o">.</span><span class="nx">php</span>
<span class="nx">application_config</span><span class="o">.</span><span class="nx">php</span>
</pre></div>
</div>
<p>Furthermore, class file names should match the name of the class itself.
For example, if you have a class named <cite>Myclass</cite>, then its filename must
be <strong>Myclass.php</strong>.</p>
</div>
<div class="section" id="class-and-method-naming">
<h2><a class="toc-backref" href="#id7">Class and Method Naming</a><a class="headerlink" href="#class-and-method-naming" title="Permalink to this headline">¶</a></h2>
<p>Class names should always start with an uppercase letter. Multiple words
should be separated with an underscore, and not CamelCased.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">superclass</span>
<span class="k">class</span> <span class="nc">SuperClass</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Super_class</span>
</pre></div>
</div>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Super_class</span> <span class="p">{</span>

        <span class="k">public</span> <span class="k">function</span> <span class="nf">__construct</span><span class="p">()</span>
        <span class="p">{</span>

        <span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Class methods should be entirely lowercased and named to clearly
indicate their function, preferably including a verb. Try to avoid
overly long and verbose names. Multiple words should be separated
with an underscore.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">function</span> <span class="nf">fileproperties</span><span class="p">()</span>               <span class="c1">// not descriptive and needs underscore separator</span>
<span class="k">function</span> <span class="nf">fileProperties</span><span class="p">()</span>               <span class="c1">// not descriptive and uses CamelCase</span>
<span class="k">function</span> <span class="nf">getfileproperties</span><span class="p">()</span>            <span class="c1">// Better!  But still missing underscore separator</span>
<span class="k">function</span> <span class="nf">getFileProperties</span><span class="p">()</span>            <span class="c1">// uses CamelCase</span>
<span class="k">function</span> <span class="nf">get_the_file_properties_from_the_file</span><span class="p">()</span>        <span class="c1">// wordy</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">function</span> <span class="nf">get_file_properties</span><span class="p">()</span>  <span class="c1">// descriptive, underscore separator, and all lowercase letters</span>
</pre></div>
</div>
</div>
<div class="section" id="variable-names">
<h2><a class="toc-backref" href="#id8">Variable Names</a><a class="headerlink" href="#variable-names" title="Permalink to this headline">¶</a></h2>
<p>The guidelines for variable naming are very similar to those used for
class methods. Variables should contain only lowercase letters,
use underscore separators, and be reasonably named to indicate their
purpose and contents. Very short, non-word variables should only be used
as iterators in for() loops.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$j</span> <span class="o">=</span> <span class="s1">&#39;foo&#39;</span><span class="p">;</span>             <span class="c1">// single letter variables should only be used in for() loops</span>
<span class="nv">$Str</span>                    <span class="c1">// contains uppercase letters</span>
<span class="nv">$bufferedText</span>           <span class="c1">// uses CamelCasing, and could be shortened without losing semantic meaning</span>
<span class="nv">$groupid</span>                <span class="c1">// multiple words, needs underscore separator</span>
<span class="nv">$name_of_last_city_used</span> <span class="c1">// too long</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="p">(</span><span class="nv">$j</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="nv">$j</span> <span class="o">&lt;</span> <span class="mi">10</span><span class="p">;</span> <span class="nv">$j</span><span class="o">++</span><span class="p">)</span>
<span class="nv">$str</span>
<span class="nv">$buffer</span>
<span class="nv">$group_id</span>
<span class="nv">$last_city</span>
</pre></div>
</div>
</div>
<div class="section" id="commenting">
<h2><a class="toc-backref" href="#id9">Commenting</a><a class="headerlink" href="#commenting" title="Permalink to this headline">¶</a></h2>
<p>In general, code should be commented prolifically. It not only helps
describe the flow and intent of the code for less experienced
programmers, but can prove invaluable when returning to your own code
months down the line. There is not a required format for comments, but
the following are recommended.</p>
<p><a class="reference external" href="http://manual.phpdoc.org/HTMLSmartyConverter/HandS/phpDocumentor/tutorial_phpDocumentor.howto.pkg.html#basics.docblock">DocBlock</a>
style comments preceding class, method, and property declarations so they can be
picked up by IDEs:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="sd">/**</span>
<span class="sd"> * Super Class</span>
<span class="sd"> *</span>
<span class="sd"> * @package     Package Name</span>
<span class="sd"> * @subpackage  Subpackage</span>
<span class="sd"> * @category    Category</span>
<span class="sd"> * <AUTHOR> Name</span>
<span class="sd"> * @link        http://example.com</span>
<span class="sd"> */</span>
<span class="k">class</span> <span class="nc">Super_class</span> <span class="p">{</span>
</pre></div>
</div>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="sd">/**</span>
<span class="sd"> * Encodes string for use in XML</span>
<span class="sd"> *</span>
<span class="sd"> * @param       string  $str    Input string</span>
<span class="sd"> * @return      string</span>
<span class="sd"> */</span>
<span class="k">function</span> <span class="nf">xml_encode</span><span class="p">(</span><span class="nv">$str</span><span class="p">)</span>
</pre></div>
</div>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="sd">/**</span>
<span class="sd"> * Data for class manipulation</span>
<span class="sd"> *</span>
<span class="sd"> * @var array</span>
<span class="sd"> */</span>
<span class="k">public</span> <span class="nv">$data</span> <span class="o">=</span> <span class="k">array</span><span class="p">();</span>
</pre></div>
</div>
<p>Use single line comments within code, leaving a blank line between large
comment blocks and code.</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="c1">// break up the string by newlines</span>
<span class="nv">$parts</span> <span class="o">=</span> <span class="nb">explode</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">,</span> <span class="nv">$str</span><span class="p">);</span>

<span class="c1">// A longer comment that needs to give greater detail on what is</span>
<span class="c1">// occurring and why can use multiple single-line comments.  Try to</span>
<span class="c1">// keep the width reasonable, around 70 characters is the easiest to</span>
<span class="c1">// read.  Don&#39;t hesitate to link to permanent external resources</span>
<span class="c1">// that may provide greater detail:</span>
<span class="c1">//</span>
<span class="c1">// http://example.com/information_about_something/in_particular/</span>

<span class="nv">$parts</span> <span class="o">=</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">foo</span><span class="p">(</span><span class="nv">$parts</span><span class="p">);</span>
</pre></div>
</div>
</div>
<div class="section" id="constants">
<h2><a class="toc-backref" href="#id10">Constants</a><a class="headerlink" href="#constants" title="Permalink to this headline">¶</a></h2>
<p>Constants follow the same guidelines as do variables, except constants
should always be fully uppercase. <em>Always use CodeIgniter constants when
appropriate, i.e. SLASH, LD, RD, PATH_CACHE, etc.</em></p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nx">myConstant</span>      <span class="c1">// missing underscore separator and not fully uppercase</span>
<span class="nx">N</span>               <span class="c1">// no single-letter constants</span>
<span class="nx">S_C_VER</span>         <span class="c1">// not descriptive</span>
<span class="nv">$str</span> <span class="o">=</span> <span class="nb">str_replace</span><span class="p">(</span><span class="s1">&#39;{foo}&#39;</span><span class="p">,</span> <span class="s1">&#39;bar&#39;</span><span class="p">,</span> <span class="nv">$str</span><span class="p">);</span>       <span class="c1">// should use LD and RD constants</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nx">MY_CONSTANT</span>
<span class="nx">NEWLINE</span>
<span class="nx">SUPER_CLASS_VERSION</span>
<span class="nv">$str</span> <span class="o">=</span> <span class="nb">str_replace</span><span class="p">(</span><span class="nx">LD</span><span class="o">.</span><span class="s1">&#39;foo&#39;</span><span class="o">.</span><span class="nx">RD</span><span class="p">,</span> <span class="s1">&#39;bar&#39;</span><span class="p">,</span> <span class="nv">$str</span><span class="p">);</span>
</pre></div>
</div>
</div>
<div class="section" id="true-false-and-null">
<h2><a class="toc-backref" href="#id11">TRUE, FALSE, and NULL</a><a class="headerlink" href="#true-false-and-null" title="Permalink to this headline">¶</a></h2>
<p><strong>TRUE</strong>, <strong>FALSE</strong>, and <strong>NULL</strong> keywords should always be fully
uppercase.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="p">(</span><span class="nv">$foo</span> <span class="o">==</span> <span class="k">true</span><span class="p">)</span>
<span class="nv">$bar</span> <span class="o">=</span> <span class="k">false</span><span class="p">;</span>
<span class="k">function</span> <span class="nf">foo</span><span class="p">(</span><span class="nv">$bar</span> <span class="o">=</span> <span class="k">null</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="p">(</span><span class="nv">$foo</span> <span class="o">==</span> <span class="k">TRUE</span><span class="p">)</span>
<span class="nv">$bar</span> <span class="o">=</span> <span class="k">FALSE</span><span class="p">;</span>
<span class="k">function</span> <span class="nf">foo</span><span class="p">(</span><span class="nv">$bar</span> <span class="o">=</span> <span class="k">NULL</span><span class="p">)</span>
</pre></div>
</div>
</div>
<div class="section" id="logical-operators">
<h2><a class="toc-backref" href="#id12">Logical Operators</a><a class="headerlink" href="#logical-operators" title="Permalink to this headline">¶</a></h2>
<p>Use of the <code class="docutils literal"><span class="pre">||</span></code> “or” comparison operator is discouraged, as its clarity
on some output devices is low (looking like the number 11, for instance).
<code class="docutils literal"><span class="pre">&amp;&amp;</span></code> is preferred over <code class="docutils literal"><span class="pre">AND</span></code> but either are acceptable, and a space should
always precede and follow <code class="docutils literal"><span class="pre">!</span></code>.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="p">(</span><span class="nv">$foo</span> <span class="o">||</span> <span class="nv">$bar</span><span class="p">)</span>
<span class="k">if</span> <span class="p">(</span><span class="nv">$foo</span> <span class="k">AND</span> <span class="nv">$bar</span><span class="p">)</span>  <span class="c1">// okay but not recommended for common syntax highlighting applications</span>
<span class="k">if</span> <span class="p">(</span><span class="o">!</span><span class="nv">$foo</span><span class="p">)</span>
<span class="k">if</span> <span class="p">(</span><span class="o">!</span> <span class="nb">is_array</span><span class="p">(</span><span class="nv">$foo</span><span class="p">))</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="p">(</span><span class="nv">$foo</span> <span class="k">OR</span> <span class="nv">$bar</span><span class="p">)</span>
<span class="k">if</span> <span class="p">(</span><span class="nv">$foo</span> <span class="o">&amp;&amp;</span> <span class="nv">$bar</span><span class="p">)</span> <span class="c1">// recommended</span>
<span class="k">if</span> <span class="p">(</span> <span class="o">!</span> <span class="nv">$foo</span><span class="p">)</span>
<span class="k">if</span> <span class="p">(</span> <span class="o">!</span> <span class="nb">is_array</span><span class="p">(</span><span class="nv">$foo</span><span class="p">))</span>
</pre></div>
</div>
</div>
<div class="section" id="comparing-return-values-and-typecasting">
<h2><a class="toc-backref" href="#id13">Comparing Return Values and Typecasting</a><a class="headerlink" href="#comparing-return-values-and-typecasting" title="Permalink to this headline">¶</a></h2>
<p>Some PHP functions return FALSE on failure, but may also have a valid
return value of “” or 0, which would evaluate to FALSE in loose
comparisons. Be explicit by comparing the variable type when using these
return values in conditionals to ensure the return value is indeed what
you expect, and not a value that has an equivalent loose-type
evaluation.</p>
<p>Use the same stringency in returning and checking your own variables.
Use <strong>===</strong> and <strong>!==</strong> as necessary.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="c1">// If &#39;foo&#39; is at the beginning of the string, strpos will return a 0,</span>
<span class="c1">// resulting in this conditional evaluating as TRUE</span>
<span class="k">if</span> <span class="p">(</span><span class="nb">strpos</span><span class="p">(</span><span class="nv">$str</span><span class="p">,</span> <span class="s1">&#39;foo&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="k">FALSE</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="p">(</span><span class="nb">strpos</span><span class="p">(</span><span class="nv">$str</span><span class="p">,</span> <span class="s1">&#39;foo&#39;</span><span class="p">)</span> <span class="o">===</span> <span class="k">FALSE</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">function</span> <span class="nf">build_string</span><span class="p">(</span><span class="nv">$str</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
<span class="p">{</span>
        <span class="k">if</span> <span class="p">(</span><span class="nv">$str</span> <span class="o">==</span> <span class="s2">&quot;&quot;</span><span class="p">)</span> <span class="c1">// uh-oh!  What if FALSE or the integer 0 is passed as an argument?</span>
        <span class="p">{</span>

        <span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">function</span> <span class="nf">build_string</span><span class="p">(</span><span class="nv">$str</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
<span class="p">{</span>
        <span class="k">if</span> <span class="p">(</span><span class="nv">$str</span> <span class="o">===</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
        <span class="p">{</span>

        <span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p>See also information regarding <a class="reference external" href="http://php.net/manual/en/language.types.type-juggling.php#language.types.typecasting">typecasting</a>,
which can be quite useful. Typecasting has a slightly different effect
which may be desirable. When casting a variable as a string, for
instance, NULL and boolean FALSE variables become empty strings, 0 (and
other numbers) become strings of digits, and boolean TRUE becomes “1”:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$str</span> <span class="o">=</span> <span class="p">(</span><span class="nx">string</span><span class="p">)</span> <span class="nv">$str</span><span class="p">;</span> <span class="c1">// cast $str as a string</span>
</pre></div>
</div>
</div>
<div class="section" id="debugging-code">
<h2><a class="toc-backref" href="#id14">Debugging Code</a><a class="headerlink" href="#debugging-code" title="Permalink to this headline">¶</a></h2>
<p>Do not leave debugging code in your submissions, even when commented out.
Things such as <code class="docutils literal"><span class="pre">var_dump()</span></code>, <code class="docutils literal"><span class="pre">print_r()</span></code>, <code class="docutils literal"><span class="pre">die()</span></code>/<code class="docutils literal"><span class="pre">exit()</span></code> should not be included
in your code unless it serves a specific purpose other than debugging.</p>
</div>
<div class="section" id="whitespace-in-files">
<h2><a class="toc-backref" href="#id15">Whitespace in Files</a><a class="headerlink" href="#whitespace-in-files" title="Permalink to this headline">¶</a></h2>
<p>No whitespace can precede the opening PHP tag or follow the closing PHP
tag. Output is buffered, so whitespace in your files can cause output to
begin before CodeIgniter outputs its content, leading to errors and an
inability for CodeIgniter to send proper headers.</p>
</div>
<div class="section" id="compatibility">
<h2><a class="toc-backref" href="#id16">Compatibility</a><a class="headerlink" href="#compatibility" title="Permalink to this headline">¶</a></h2>
<p>CodeIgniter recommends PHP 5.6 or newer to be used, but it should be
compatible with PHP 5.3.7. Your code must either be compatible with this
requirement, provide a suitable fallback, or be an optional feature that
dies quietly without affecting a user’s application.</p>
<p>Additionally, do not use PHP functions that require non-default libraries
to be installed unless your code contains an alternative method when the
function is not available.</p>
</div>
<div class="section" id="one-file-per-class">
<h2><a class="toc-backref" href="#id17">One File per Class</a><a class="headerlink" href="#one-file-per-class" title="Permalink to this headline">¶</a></h2>
<p>Use separate files for each class, unless the classes are <em>closely related</em>.
An example of a CodeIgniter file that contains multiple classes is the
Xmlrpc library file.</p>
</div>
<div class="section" id="whitespace">
<h2><a class="toc-backref" href="#id18">Whitespace</a><a class="headerlink" href="#whitespace" title="Permalink to this headline">¶</a></h2>
<p>Use tabs for whitespace in your code, not spaces. This may seem like a
small thing, but using tabs instead of whitespace allows the developer
looking at your code to have indentation at levels that they prefer and
customize in whatever application they use. And as a side benefit, it
results in (slightly) more compact files, storing one tab character
versus, say, four space characters.</p>
</div>
<div class="section" id="line-breaks">
<h2><a class="toc-backref" href="#id19">Line Breaks</a><a class="headerlink" href="#line-breaks" title="Permalink to this headline">¶</a></h2>
<p>Files must be saved with Unix line breaks. This is more of an issue for
developers who work in Windows, but in any case ensure that your text
editor is setup to save files with Unix line breaks.</p>
</div>
<div class="section" id="code-indenting">
<h2><a class="toc-backref" href="#id20">Code Indenting</a><a class="headerlink" href="#code-indenting" title="Permalink to this headline">¶</a></h2>
<p>Use Allman style indenting. With the exception of Class declarations,
braces are always placed on a line by themselves, and indented at the
same level as the control statement that “owns” them.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">function</span> <span class="nf">foo</span><span class="p">(</span><span class="nv">$bar</span><span class="p">)</span> <span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>

<span class="k">foreach</span> <span class="p">(</span><span class="nv">$arr</span> <span class="k">as</span> <span class="nv">$key</span> <span class="o">=&gt;</span> <span class="nv">$val</span><span class="p">)</span> <span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>

<span class="k">if</span> <span class="p">(</span><span class="nv">$foo</span> <span class="o">==</span> <span class="nv">$bar</span><span class="p">)</span> <span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>

<span class="k">for</span> <span class="p">(</span><span class="nv">$i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="nv">$i</span> <span class="o">&lt;</span> <span class="mi">10</span><span class="p">;</span> <span class="nv">$i</span><span class="o">++</span><span class="p">)</span>
        <span class="p">{</span>
        <span class="k">for</span> <span class="p">(</span><span class="nv">$j</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="nv">$j</span> <span class="o">&lt;</span> <span class="mi">10</span><span class="p">;</span> <span class="nv">$j</span><span class="o">++</span><span class="p">)</span>
                <span class="p">{</span>
                <span class="c1">// ...</span>
                <span class="p">}</span>
        <span class="p">}</span>

<span class="k">try</span> <span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>
<span class="k">catch</span><span class="p">()</span> <span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">function</span> <span class="nf">foo</span><span class="p">(</span><span class="nv">$bar</span><span class="p">)</span>
<span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>

<span class="k">foreach</span> <span class="p">(</span><span class="nv">$arr</span> <span class="k">as</span> <span class="nv">$key</span> <span class="o">=&gt;</span> <span class="nv">$val</span><span class="p">)</span>
<span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>

<span class="k">if</span> <span class="p">(</span><span class="nv">$foo</span> <span class="o">==</span> <span class="nv">$bar</span><span class="p">)</span>
<span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>
<span class="k">else</span>
<span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>

<span class="k">for</span> <span class="p">(</span><span class="nv">$i</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="nv">$i</span> <span class="o">&lt;</span> <span class="mi">10</span><span class="p">;</span> <span class="nv">$i</span><span class="o">++</span><span class="p">)</span>
<span class="p">{</span>
        <span class="k">for</span> <span class="p">(</span><span class="nv">$j</span> <span class="o">=</span> <span class="mi">0</span><span class="p">;</span> <span class="nv">$j</span> <span class="o">&lt;</span> <span class="mi">10</span><span class="p">;</span> <span class="nv">$j</span><span class="o">++</span><span class="p">)</span>
        <span class="p">{</span>
                <span class="c1">// ...</span>
        <span class="p">}</span>
<span class="p">}</span>

<span class="k">try</span>
<span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>
<span class="k">catch</span><span class="p">()</span>
<span class="p">{</span>
        <span class="c1">// ...</span>
<span class="p">}</span>
</pre></div>
</div>
</div>
<div class="section" id="bracket-and-parenthetic-spacing">
<h2><a class="toc-backref" href="#id21">Bracket and Parenthetic Spacing</a><a class="headerlink" href="#bracket-and-parenthetic-spacing" title="Permalink to this headline">¶</a></h2>
<p>In general, parenthesis and brackets should not use any additional
spaces. The exception is that a space should always follow PHP control
structures that accept arguments with parenthesis (declare, do-while,
elseif, for, foreach, if, switch, while), to help distinguish them from
functions and increase readability.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$arr</span><span class="p">[</span> <span class="nv">$foo</span> <span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;foo&#39;</span><span class="p">;</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$arr</span><span class="p">[</span><span class="nv">$foo</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;foo&#39;</span><span class="p">;</span> <span class="c1">// no spaces around array keys</span>
</pre></div>
</div>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">function</span> <span class="nf">foo</span> <span class="p">(</span> <span class="nv">$bar</span> <span class="p">)</span>
<span class="p">{</span>

<span class="p">}</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">function</span> <span class="nf">foo</span><span class="p">(</span><span class="nv">$bar</span><span class="p">)</span> <span class="c1">// no spaces around parenthesis in function declarations</span>
<span class="p">{</span>

<span class="p">}</span>
</pre></div>
</div>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">foreach</span><span class="p">(</span> <span class="nv">$query</span><span class="o">-&gt;</span><span class="na">result</span><span class="p">()</span> <span class="k">as</span> <span class="nv">$row</span> <span class="p">)</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">foreach</span> <span class="p">(</span><span class="nv">$query</span><span class="o">-&gt;</span><span class="na">result</span><span class="p">()</span> <span class="k">as</span> <span class="nv">$row</span><span class="p">)</span> <span class="c1">// single space following PHP control structures, but not in interior parenthesis</span>
</pre></div>
</div>
</div>
<div class="section" id="localized-text">
<h2><a class="toc-backref" href="#id22">Localized Text</a><a class="headerlink" href="#localized-text" title="Permalink to this headline">¶</a></h2>
<p>CodeIgniter libraries should take advantage of corresponding language files
whenever possible.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">return</span> <span class="s2">&quot;Invalid Selection&quot;</span><span class="p">;</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">return</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">lang</span><span class="o">-&gt;</span><span class="na">line</span><span class="p">(</span><span class="s1">&#39;invalid_selection&#39;</span><span class="p">);</span>
</pre></div>
</div>
</div>
<div class="section" id="private-methods-and-variables">
<h2><a class="toc-backref" href="#id23">Private Methods and Variables</a><a class="headerlink" href="#private-methods-and-variables" title="Permalink to this headline">¶</a></h2>
<p>Methods and variables that are only accessed internally,
such as utility and helper functions that your public methods use for
code abstraction, should be prefixed with an underscore.</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">public</span> <span class="k">function</span> <span class="nf">convert_text</span><span class="p">()</span>
<span class="k">private</span> <span class="k">function</span> <span class="nf">_convert_text</span><span class="p">()</span>
</pre></div>
</div>
</div>
<div class="section" id="php-errors">
<h2><a class="toc-backref" href="#id24">PHP Errors</a><a class="headerlink" href="#php-errors" title="Permalink to this headline">¶</a></h2>
<p>Code must run error free and not rely on warnings and notices to be
hidden to meet this requirement. For instance, never access a variable
that you did not set yourself (such as <code class="docutils literal"><span class="pre">$_POST</span></code> array keys) without first
checking to see that it <code class="docutils literal"><span class="pre">isset()</span></code>.</p>
<p>Make sure that your dev environment has error reporting enabled
for ALL users, and that display_errors is enabled in the PHP
environment. You can check this setting with:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="p">(</span><span class="nb">ini_get</span><span class="p">(</span><span class="s1">&#39;display_errors&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">)</span>
<span class="p">{</span>
        <span class="k">exit</span> <span class="s2">&quot;Enabled&quot;</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
<p>On some servers where <em>display_errors</em> is disabled, and you do not have
the ability to change this in the php.ini, you can often enable it with:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nb">ini_set</span><span class="p">(</span><span class="s1">&#39;display_errors&#39;</span><span class="p">,</span> <span class="mi">1</span><span class="p">);</span>
</pre></div>
</div>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">Setting the <a class="reference external" href="http://php.net/manual/en/errorfunc.configuration.php#ini.display-errors">display_errors</a>
setting with <code class="docutils literal"><span class="pre">ini_set()</span></code> at runtime is not identical to having
it enabled in the PHP environment. Namely, it will not have any
effect if the script has fatal errors.</p>
</div>
</div>
<div class="section" id="short-open-tags">
<h2><a class="toc-backref" href="#id25">Short Open Tags</a><a class="headerlink" href="#short-open-tags" title="Permalink to this headline">¶</a></h2>
<p>Always use full PHP opening tags, in case a server does not have
<em>short_open_tag</em> enabled.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="o">&lt;?</span> <span class="k">echo</span> <span class="nv">$foo</span><span class="p">;</span> <span class="cp">?&gt;</span>

<span class="cp">&lt;?</span><span class="o">=</span><span class="nv">$foo</span><span class="cp">?&gt;</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="o">&lt;?</span><span class="nx">php</span> <span class="k">echo</span> <span class="nv">$foo</span><span class="p">;</span> <span class="cp">?&gt;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">PHP 5.4 will always have the <strong>&lt;?=</strong> tag available.</p>
</div>
</div>
<div class="section" id="one-statement-per-line">
<h2><a class="toc-backref" href="#id26">One Statement Per Line</a><a class="headerlink" href="#one-statement-per-line" title="Permalink to this headline">¶</a></h2>
<p>Never combine statements on one line.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$foo</span> <span class="o">=</span> <span class="s1">&#39;this&#39;</span><span class="p">;</span> <span class="nv">$bar</span> <span class="o">=</span> <span class="s1">&#39;that&#39;</span><span class="p">;</span> <span class="nv">$bat</span> <span class="o">=</span> <span class="nb">str_replace</span><span class="p">(</span><span class="nv">$foo</span><span class="p">,</span> <span class="nv">$bar</span><span class="p">,</span> <span class="nv">$bag</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$foo</span> <span class="o">=</span> <span class="s1">&#39;this&#39;</span><span class="p">;</span>
<span class="nv">$bar</span> <span class="o">=</span> <span class="s1">&#39;that&#39;</span><span class="p">;</span>
<span class="nv">$bat</span> <span class="o">=</span> <span class="nb">str_replace</span><span class="p">(</span><span class="nv">$foo</span><span class="p">,</span> <span class="nv">$bar</span><span class="p">,</span> <span class="nv">$bag</span><span class="p">);</span>
</pre></div>
</div>
</div>
<div class="section" id="strings">
<h2><a class="toc-backref" href="#id27">Strings</a><a class="headerlink" href="#strings" title="Permalink to this headline">¶</a></h2>
<p>Always use single quoted strings unless you need variables parsed, and
in cases where you do need variables parsed, use braces to prevent
greedy token parsing. You may also use double-quoted strings if the
string contains single quotes, so you do not have to use escape
characters.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="s2">&quot;My String&quot;</span>                                     <span class="c1">// no variable parsing, so no use for double quotes</span>
<span class="s2">&quot;My string </span><span class="si">$foo</span><span class="s2">&quot;</span>                                <span class="c1">// needs braces</span>
<span class="s1">&#39;SELECT foo FROM bar WHERE baz = \&#39;bag\&#39;&#39;</span>       <span class="c1">// ugly</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="s1">&#39;My String&#39;</span>
<span class="s2">&quot;My string </span><span class="si">{</span><span class="nv">$foo</span><span class="si">}</span><span class="s2">&quot;</span>
<span class="s2">&quot;SELECT foo FROM bar WHERE baz = &#39;bag&#39;&quot;</span>
</pre></div>
</div>
</div>
<div class="section" id="sql-queries">
<h2><a class="toc-backref" href="#id28">SQL Queries</a><a class="headerlink" href="#sql-queries" title="Permalink to this headline">¶</a></h2>
<p>SQL keywords are always capitalized: SELECT, INSERT, UPDATE, WHERE,
AS, JOIN, ON, IN, etc.</p>
<p>Break up long queries into multiple lines for legibility, preferably
breaking for each clause.</p>
<p><strong>INCORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="c1">// keywords are lowercase and query is too long for</span>
<span class="c1">// a single line (... indicates continuation of line)</span>
<span class="nv">$query</span> <span class="o">=</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">db</span><span class="o">-&gt;</span><span class="na">query</span><span class="p">(</span><span class="s2">&quot;select foo, bar, baz, foofoo, foobar as raboof, foobaz from exp_pre_email_addresses</span>
<span class="s2">...where foo != &#39;oof&#39; and baz != &#39;zab&#39; order by foobaz limit 5, 100&quot;</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>CORRECT</strong>:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$query</span> <span class="o">=</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">db</span><span class="o">-&gt;</span><span class="na">query</span><span class="p">(</span><span class="s2">&quot;SELECT foo, bar, baz, foofoo, foobar AS raboof, foobaz</span>
<span class="s2">                                FROM exp_pre_email_addresses</span>
<span class="s2">                                WHERE foo != &#39;oof&#39;</span>
<span class="s2">                                AND baz != &#39;zab&#39;</span>
<span class="s2">                                ORDER BY foobaz</span>
<span class="s2">                                LIMIT 5, 100&quot;</span><span class="p">);</span>
</pre></div>
</div>
</div>
<div class="section" id="default-function-arguments">
<h2><a class="toc-backref" href="#id29">Default Function Arguments</a><a class="headerlink" href="#default-function-arguments" title="Permalink to this headline">¶</a></h2>
<p>Whenever appropriate, provide function argument defaults, which helps
prevent PHP errors with mistaken calls and provides common fallback
values which can save a few lines of code. Example:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">function</span> <span class="nf">foo</span><span class="p">(</span><span class="nv">$bar</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span><span class="p">,</span> <span class="nv">$baz</span> <span class="o">=</span> <span class="k">FALSE</span><span class="p">)</span>
</pre></div>
</div>
</div>
</div>


          </div>
          <footer>
  
    <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
      
        <a href="../libraries/index.html" class="btn btn-neutral float-right" title="Libraries">Next <span class="fa fa-arrow-circle-right"></span></a>
      
      
        <a href="security.html" class="btn btn-neutral" title="Security"><span class="fa fa-arrow-circle-left"></span> Previous</a>
      
    </div>
  

  <hr/>

  <div role="contentinfo">
    <p>
        &copy; Copyright 2014 - 2019, British Columbia Institute of Technology.
      Last updated on Sep 19, 2019.
    </p>
  </div>

  Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a href="https://github.com/snide/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
</footer>
        </div>
      </div>

    </section>

  </div>
  


  

    <script type="text/javascript">
        var DOCUMENTATION_OPTIONS = {
            URL_ROOT:'../',
            VERSION:'3.1.11',
            COLLAPSE_INDEX:false,
            FILE_SUFFIX:'.html',
            HAS_SOURCE:  false
        };
    </script>
      <script type="text/javascript" src="../_static/jquery.js"></script>
      <script type="text/javascript" src="../_static/underscore.js"></script>
      <script type="text/javascript" src="../_static/doctools.js"></script>

  

  
  
    <script type="text/javascript" src="../_static/js/theme.js"></script>
  

  
  
  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.StickyNav.enable();
      });
  </script>
   

</body>
</html>